name: marketinya
description: 'Marketinya is an all-in-one marketing platform designed to help businesses efficiently create, manage, and optimize campaigns across multiple channels.'
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.7

environment:
  sdk: ^3.6.0
  flutter: ^3.27.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  carousel_slider: ^5.0.0
  cloud_firestore: ^5.4.5
  cupertino_icons: ^1.0.8
  dartx: ^1.2.0
  equatable: ^2.0.3
  file_picker: ^10.1.9
  firebase_auth: ^5.5.2
  firebase_core: ^3.7.0
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.0
  flutter_dropzone: ^4.2.1
  flutter_svg: ^2.0.10+1
  font_awesome_flutter: ^10.7.0
  freezed_annotation: ^2.4.4
  get_it: ^8.0.3
  go_router: ^15.1.2
  google_fonts: ^6.2.1
  injectable: ^2.5.0
  intl: ^0.19.0
  json_annotation: ^4.9.0
  logger: ^2.5.0
  meta: ^1.15.0
  path_provider: ^2.1.5
  shared_preferences: ^2.5.3
  url_strategy: ^0.2.0

dev_dependencies:
  build_runner: ^2.4.15
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter
  freezed: ^2.5.8
  json_serializable: ^6.9.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/carousel/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: GothamPro
      fonts:
        - asset: assets/fonts/gothampro.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages