analyzer:
  exclude:
    - '**/*.g.dart'
    - '**/*.freezed.dart'
    - '**/*.config.dart'
    - '**/*.gr.dart'
    - '**/*.gen.dart'
    - '**/*.i69n.dart'
    - '**/feature**/*_test.dart'

  language:
    strict-casts: true
    strict-raw-types: true

  errors:
    missing_required_param: error
    missing_return: error
    invalid_assignment: warning
    invalid_annotation_target: ignore

linter:
  rules:
    always_declare_return_types: true
    always_put_control_body_on_new_line: true
    #always_put_required_named_parameters_first: true
    always_use_package_imports: true
    annotate_overrides: true
    avoid_bool_literals_in_conditional_expressions: true
    avoid_catching_errors: true
    avoid_dynamic_calls: true
    avoid_empty_else: true
    avoid_equals_and_hash_code_on_mutable_classes: true
    avoid_escaping_inner_quotes: true
    avoid_field_initializers_in_const_classes: true
    avoid_function_literals_in_foreach_calls: true
    avoid_implementing_value_types: true
    avoid_init_to_null: true
    avoid_multiple_declarations_per_line: true
    avoid_null_checks_in_equality_operators: true
    avoid_print: true
    avoid_private_typedef_functions: true
    avoid_relative_lib_imports: true
    avoid_renaming_method_parameters: true
    avoid_return_types_on_setters: true
    avoid_returning_null_for_void: true
    avoid_setters_without_getters: true
    avoid_shadowing_type_parameters: true
    avoid_single_cascade_in_expression_statements: true
    avoid_type_to_string: true
    avoid_types_as_parameter_names: true
    avoid_unnecessary_containers: true
    avoid_unused_constructor_parameters: true
    avoid_void_async: true
    avoid_web_libraries_in_flutter: true
    await_only_futures: true
    camel_case_extensions: true
    camel_case_types: true
    cancel_subscriptions: true
    cast_nullable_to_non_nullable: true
    close_sinks: true
    collection_methods_unrelated_type: true
    combinators_ordering: true
    constant_identifier_names: true
    control_flow_in_finally: true
    curly_braces_in_flow_control_structures: true
    deprecated_consistency: true
    directives_ordering: true
    do_not_use_environment: true
    empty_catches: true
    empty_constructor_bodies: true
    empty_statements: true
    exhaustive_cases: true
    file_names: true
    flutter_style_todos: true
    hash_and_equals: true
    implementation_imports: true
    invalid_case_patterns: true
    join_return_with_assignment: true
    leading_newlines_in_multiline_strings: false
    library_names: true
    library_prefixes: true
    literal_only_boolean_expressions: true
    missing_whitespace_between_adjacent_strings: true
    no_adjacent_strings_in_list: true
    no_duplicate_case_values: true
    no_logic_in_create_state: true
    no_runtimeType_toString: true
    non_constant_identifier_names: true
    noop_primitive_operations: true
    null_check_on_nullable_type_parameter: true
    null_closures: true
    omit_local_variable_types: true
    only_throw_errors: true
    overridden_fields: true
    package_names: true
    package_prefixed_library_names: true
    parameter_assignments: true
    prefer_adjacent_string_concatenation: true
    prefer_asserts_in_initializer_lists: true
    prefer_collection_literals: true
    prefer_conditional_assignment: true
    prefer_const_constructors: true
    prefer_const_constructors_in_immutables: true
    prefer_const_declarations: true
    prefer_const_literals_to_create_immutables: true
    prefer_constructors_over_static_methods: true
    prefer_contains: true
    prefer_final_fields: true
    prefer_final_in_for_each: true
    prefer_final_locals: true
    prefer_for_elements_to_map_fromIterable: true
    prefer_function_declarations_over_variables: true
    prefer_generic_function_type_aliases: true
    prefer_if_elements_to_conditional_expressions: true
    prefer_if_null_operators: true
    prefer_initializing_formals: true
    prefer_inlined_adds: true
    prefer_interpolation_to_compose_strings: true
    prefer_is_empty: true
    prefer_is_not_empty: true
    prefer_is_not_operator: true
    prefer_iterable_whereType: true
    prefer_null_aware_method_calls: true
    prefer_null_aware_operators: true
    prefer_spread_collections: true
    prefer_typing_uninitialized_variables: true
    prefer_void_to_null: true
    provide_deprecation_message: true
    recursive_getters: true
    require_trailing_commas: true
    sized_box_for_whitespace: true
    slash_for_doc_comments: true
    sort_child_properties_last: true
    sort_constructors_first: true
    sort_pub_dependencies: true
    sort_unnamed_constructors_first: true
    test_types_in_equals: true
    throw_in_finally: true
    tighten_type_of_initializing_formals: true
    type_annotate_public_apis: true
    type_init_formals: true
    unawaited_futures: true
    unnecessary_await_in_return: true
    unnecessary_brace_in_string_interps: true
    unnecessary_const: true
    unnecessary_constructor_name: true
    unnecessary_getters_setters: true
    unnecessary_library_directive: true
    unnecessary_new: true
    unnecessary_null_aware_assignments: true
    unnecessary_null_checks: true
    unnecessary_null_in_if_null_operators: true
    unnecessary_nullable_for_final_variable_declarations: true
    unnecessary_overrides: true
    unnecessary_parenthesis: true
    unnecessary_raw_strings: true
    unnecessary_statements: true
    unnecessary_string_escapes: true
    unnecessary_string_interpolations: true
    unnecessary_this: true
    unreachable_from_main: true
    unrelated_type_equality_checks: true
    use_build_context_synchronously: true
    use_full_hex_values_for_flutter_colors: true
    use_function_type_syntax_for_parameters: true
    use_if_null_to_convert_nulls_to_bools: true
    use_is_even_rather_than_modulo: true
    use_key_in_widget_constructors: true
    use_late_for_private_fields_and_variables: true
    use_named_constants: true
    use_rethrow_when_possible: true
    use_setters_to_change_properties: true
    use_string_buffers: true
    use_string_in_part_of_directives: true
    use_test_throws_matchers: true
    valid_regexps: true
    void_checks: true