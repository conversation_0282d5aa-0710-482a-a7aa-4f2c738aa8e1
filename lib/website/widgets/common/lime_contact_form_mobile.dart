import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:marketinya/core/config/service_locator.dart';
import 'package:marketinya/core/design_system/atoms/spaces.dart';
import 'package:marketinya/core/extensions/context_extension.dart';
import 'package:marketinya/core/repositories/contact_repository.dart';
import 'package:marketinya/core/utils/color_utils.dart';
import 'package:marketinya/website/models/contact_model.dart';

class LimeContactFormMobile extends StatefulWidget {
  const LimeContactFormMobile({super.key});

  @override
  State<LimeContactFormMobile> createState() => _LimeContactFormMobileState();
}

class _LimeContactFormMobileState extends State<LimeContactFormMobile> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _nameController = TextEditingController();

  final TextEditingController _telephoneController = TextEditingController();

  final TextEditingController _firmNameController = TextEditingController();

  final TextEditingController _emailController = TextEditingController();

  final TextEditingController _questionController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _telephoneController.dispose();
    _firmNameController.dispose();
    _emailController.dispose();
    _questionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          _buildTitle('Свържете се с нас за\nбезплатна консултация', 26),
          _buildTitle(
            'Искате ли да знаете какво\nможем да направим за вас?\nПопълнете формата и нека се\nопознаем. Без обвързвания, без\nдосадни търговски\nпредложения, и двамата сме\nтвърде заети за това.',
            20,
          ),
          _buildFormField('Име и фамилия', 'Име, Фамилия', _nameController),
          _buildFormField('Телефон за връзка', 'Телефон', _telephoneController),
          _buildFormField('Име на фирма', 'Фирма', _firmNameController),
          _buildFormField('Имейл адрес', 'Имейл', _emailController),
          _bigInputField(),
          _sendButton(context),
        ],
      ),
    );
  }

  Widget _buildFormField(
    String labelText,
    String placeholderText,
    TextEditingController controller,
  ) {
    return Padding(
      padding: const EdgeInsets.only(left: xxsPlus, right: xxsPlus, top: xs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            labelText,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w400),
          ),
          TextFormField(
            controller: controller,
            style: const TextStyle(color: Colors.black),
            decoration: InputDecoration(
              filled: true,
              fillColor: ColorUtils.lightGray,
              contentPadding:
                  const EdgeInsets.symmetric(vertical: xxsPlus, horizontal: 20),
              labelText: placeholderText,
              labelStyle: const TextStyle(color: Colors.black),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(xs),
              ),
              floatingLabelBehavior: FloatingLabelBehavior.never,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Полето е задължително';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _bigInputField() {
    return Padding(
      padding: const EdgeInsets.only(left: xxsPlus, right: xxsPlus, top: xs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Задай въпрос',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.w400),
          ),
          const SizedBox(height: xxsPlus),
          SizedBox(
            height: 110,
            child: TextFormField(
              controller: _questionController,
              expands: true,
              maxLines: null,
              minLines: null,
              style: const TextStyle(color: Colors.black),
              textAlignVertical: TextAlignVertical.top,
              decoration: InputDecoration(
                filled: true,
                fillColor: ColorUtils.lightGray,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(xs),
                ),
                floatingLabelBehavior: FloatingLabelBehavior.never,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Полето е задължително';
                }
                return null;
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _sendButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: md, left: xxsPlus, right: xxsPlus, bottom: lg),
      child: ElevatedButton(
        onPressed: () async {
          if (_formKey.currentState!.validate()) {
            final contactModel = ContactModel(
              _nameController.text,
              _telephoneController.text,
              _firmNameController.text,
              _emailController.text,
              _questionController.text,
              DateTime.now().toString(),
            );

            try {
              await getIt<ContactRepository>().sendQuestion(contactModel);
              if (context.mounted) {
                context.showSuccessSnackBar('Въпросът Ви е изпратен успешно.');
              }
            } catch (e) {
              if (context.mounted) {
                context.showFailureSnackBar(
                    'Възникна проблем с изпращането на въпроса',
                );
              }
            }
          }
        },
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(xxsPlus),
          ),
        ),
        child: const Padding(
          padding: EdgeInsets.all(xs),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Изпрати',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: xxsPlus),
              Icon(Icons.arrow_forward),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(String text, double fontSize) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: xxsPlus, vertical: 20),
      child: Text(
        text,
        style: GoogleFonts.roboto(
          color: ColorUtils.lightGray,
          textStyle: TextStyle(
            fontSize: fontSize,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }
}
