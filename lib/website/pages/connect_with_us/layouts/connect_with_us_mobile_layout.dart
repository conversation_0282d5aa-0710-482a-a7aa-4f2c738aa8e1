import 'package:flutter/material.dart';
import 'package:marketinya/website/widgets/common/lime_contact_form_mobile.dart';
import 'package:marketinya/website/widgets/common/subscription_form_mobile.dart';
import 'package:marketinya/website/widgets/footer/footer_mobile.dart';
import 'package:marketinya/website/widgets/home/<USER>/business_info_section.dart';
import 'package:marketinya/website/widgets/home/<USER>/explore_services_section_mobile.dart';

class ConnectWithUsMobileLayout extends StatelessWidget {
  const ConnectWithUsMobileLayout({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: SingleChildScrollView(
        child: <PERSON>umn(
          children: [
            LimeContactFormMobile(),
            ExploreServicesSectionMobile(),
            BusinessInfoSectionMobile(),
            SubscriptionFormMobile(),
            FooterMobile(),
          ],
        ),
      ),
    );
  }
}