import 'package:flutter/material.dart';
import 'package:marketinya/website/widgets/common/subscription_form.dart';
import 'package:marketinya/website/widgets/footer/footer.dart';
import 'package:marketinya/website/widgets/home/<USER>';
import 'package:marketinya/website/widgets/services/free_consultation_section.dart';
import 'package:marketinya/website/widgets/services/services_list.dart';
import 'package:marketinya/website/widgets/services/text_section.dart';

class ServiceDesktopLayout extends StatelessWidget {
  const ServiceDesktopLayout({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            const TextSection(),
            const FreeConsultationSection(),
            const ServicesList(),
            _headerWhyAreWeDifferent(),
            const FeaturesWithImagesSection(),
            const SubscriptionForm(),
            const Footer(),
          ],
        ),
      ),
    );
  }

  Padding _headerWhyAreWeDifferent() {
    return const Padding(
      padding: EdgeInsets.only(top: 72, bottom: 72),
      child: Text(
        'Какво ни различава от останалите?',
        style: TextStyle(
          fontSize: 52,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
