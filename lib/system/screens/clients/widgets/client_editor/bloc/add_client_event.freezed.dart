// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_client_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AddClientEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddClientEventCopyWith<$Res> {
  factory $AddClientEventCopyWith(
          AddClientEvent value, $Res Function(AddClientEvent) then) =
      _$AddClientEventCopyWithImpl<$Res, AddClientEvent>;
}

/// @nodoc
class _$AddClientEventCopyWithImpl<$Res, $Val extends AddClientEvent>
    implements $AddClientEventCopyWith<$Res> {
  _$AddClientEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadImplCopyWith<$Res> {
  factory _$$LoadImplCopyWith(
          _$LoadImpl value, $Res Function(_$LoadImpl) then) =
      __$$LoadImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$LoadImpl>
    implements _$$LoadImplCopyWith<$Res> {
  __$$LoadImplCopyWithImpl(_$LoadImpl _value, $Res Function(_$LoadImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadImpl implements _Load {
  const _$LoadImpl();

  @override
  String toString() {
    return 'AddClientEvent.load()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return load();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return load?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (load != null) {
      return load();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return load(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return load?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (load != null) {
      return load(this);
    }
    return orElse();
  }
}

abstract class _Load implements AddClientEvent {
  const factory _Load() = _$LoadImpl;
}

/// @nodoc
abstract class _$$CompanyNameChangedImplCopyWith<$Res> {
  factory _$$CompanyNameChangedImplCopyWith(_$CompanyNameChangedImpl value,
          $Res Function(_$CompanyNameChangedImpl) then) =
      __$$CompanyNameChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$CompanyNameChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$CompanyNameChangedImpl>
    implements _$$CompanyNameChangedImplCopyWith<$Res> {
  __$$CompanyNameChangedImplCopyWithImpl(_$CompanyNameChangedImpl _value,
      $Res Function(_$CompanyNameChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$CompanyNameChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CompanyNameChangedImpl implements _CompanyNameChanged {
  const _$CompanyNameChangedImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'AddClientEvent.companyNameChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompanyNameChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompanyNameChangedImplCopyWith<_$CompanyNameChangedImpl> get copyWith =>
      __$$CompanyNameChangedImplCopyWithImpl<_$CompanyNameChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return companyNameChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return companyNameChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (companyNameChanged != null) {
      return companyNameChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return companyNameChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return companyNameChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (companyNameChanged != null) {
      return companyNameChanged(this);
    }
    return orElse();
  }
}

abstract class _CompanyNameChanged implements AddClientEvent {
  const factory _CompanyNameChanged(final String value) =
      _$CompanyNameChangedImpl;

  String get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompanyNameChangedImplCopyWith<_$CompanyNameChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NameChangedImplCopyWith<$Res> {
  factory _$$NameChangedImplCopyWith(
          _$NameChangedImpl value, $Res Function(_$NameChangedImpl) then) =
      __$$NameChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$NameChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$NameChangedImpl>
    implements _$$NameChangedImplCopyWith<$Res> {
  __$$NameChangedImplCopyWithImpl(
      _$NameChangedImpl _value, $Res Function(_$NameChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$NameChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$NameChangedImpl implements _NameChanged {
  const _$NameChangedImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'AddClientEvent.nameChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NameChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NameChangedImplCopyWith<_$NameChangedImpl> get copyWith =>
      __$$NameChangedImplCopyWithImpl<_$NameChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return nameChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return nameChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (nameChanged != null) {
      return nameChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return nameChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return nameChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (nameChanged != null) {
      return nameChanged(this);
    }
    return orElse();
  }
}

abstract class _NameChanged implements AddClientEvent {
  const factory _NameChanged(final String value) = _$NameChangedImpl;

  String get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NameChangedImplCopyWith<_$NameChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DateOfBirthChangedImplCopyWith<$Res> {
  factory _$$DateOfBirthChangedImplCopyWith(_$DateOfBirthChangedImpl value,
          $Res Function(_$DateOfBirthChangedImpl) then) =
      __$$DateOfBirthChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$DateOfBirthChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$DateOfBirthChangedImpl>
    implements _$$DateOfBirthChangedImplCopyWith<$Res> {
  __$$DateOfBirthChangedImplCopyWithImpl(_$DateOfBirthChangedImpl _value,
      $Res Function(_$DateOfBirthChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$DateOfBirthChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DateOfBirthChangedImpl implements _DateOfBirthChanged {
  const _$DateOfBirthChangedImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'AddClientEvent.dateOfBirthChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DateOfBirthChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DateOfBirthChangedImplCopyWith<_$DateOfBirthChangedImpl> get copyWith =>
      __$$DateOfBirthChangedImplCopyWithImpl<_$DateOfBirthChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return dateOfBirthChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return dateOfBirthChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (dateOfBirthChanged != null) {
      return dateOfBirthChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return dateOfBirthChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return dateOfBirthChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (dateOfBirthChanged != null) {
      return dateOfBirthChanged(this);
    }
    return orElse();
  }
}

abstract class _DateOfBirthChanged implements AddClientEvent {
  const factory _DateOfBirthChanged(final String value) =
      _$DateOfBirthChangedImpl;

  String get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DateOfBirthChangedImplCopyWith<_$DateOfBirthChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BusinessSectorChangedImplCopyWith<$Res> {
  factory _$$BusinessSectorChangedImplCopyWith(
          _$BusinessSectorChangedImpl value,
          $Res Function(_$BusinessSectorChangedImpl) then) =
      __$$BusinessSectorChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BusinessSector value});
}

/// @nodoc
class __$$BusinessSectorChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$BusinessSectorChangedImpl>
    implements _$$BusinessSectorChangedImplCopyWith<$Res> {
  __$$BusinessSectorChangedImplCopyWithImpl(_$BusinessSectorChangedImpl _value,
      $Res Function(_$BusinessSectorChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$BusinessSectorChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as BusinessSector,
    ));
  }
}

/// @nodoc

class _$BusinessSectorChangedImpl implements _BusinessSectorChanged {
  const _$BusinessSectorChangedImpl(this.value);

  @override
  final BusinessSector value;

  @override
  String toString() {
    return 'AddClientEvent.businessSectorChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BusinessSectorChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BusinessSectorChangedImplCopyWith<_$BusinessSectorChangedImpl>
      get copyWith => __$$BusinessSectorChangedImplCopyWithImpl<
          _$BusinessSectorChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return businessSectorChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return businessSectorChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (businessSectorChanged != null) {
      return businessSectorChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return businessSectorChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return businessSectorChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (businessSectorChanged != null) {
      return businessSectorChanged(this);
    }
    return orElse();
  }
}

abstract class _BusinessSectorChanged implements AddClientEvent {
  const factory _BusinessSectorChanged(final BusinessSector value) =
      _$BusinessSectorChangedImpl;

  BusinessSector get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BusinessSectorChangedImplCopyWith<_$BusinessSectorChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CompanyIdChangedImplCopyWith<$Res> {
  factory _$$CompanyIdChangedImplCopyWith(_$CompanyIdChangedImpl value,
          $Res Function(_$CompanyIdChangedImpl) then) =
      __$$CompanyIdChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$CompanyIdChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$CompanyIdChangedImpl>
    implements _$$CompanyIdChangedImplCopyWith<$Res> {
  __$$CompanyIdChangedImplCopyWithImpl(_$CompanyIdChangedImpl _value,
      $Res Function(_$CompanyIdChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$CompanyIdChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CompanyIdChangedImpl implements _CompanyIdChanged {
  const _$CompanyIdChangedImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'AddClientEvent.companyIdChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompanyIdChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompanyIdChangedImplCopyWith<_$CompanyIdChangedImpl> get copyWith =>
      __$$CompanyIdChangedImplCopyWithImpl<_$CompanyIdChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return companyIdChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return companyIdChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (companyIdChanged != null) {
      return companyIdChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return companyIdChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return companyIdChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (companyIdChanged != null) {
      return companyIdChanged(this);
    }
    return orElse();
  }
}

abstract class _CompanyIdChanged implements AddClientEvent {
  const factory _CompanyIdChanged(final String value) = _$CompanyIdChangedImpl;

  String get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompanyIdChangedImplCopyWith<_$CompanyIdChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PersonalIdChangedImplCopyWith<$Res> {
  factory _$$PersonalIdChangedImplCopyWith(_$PersonalIdChangedImpl value,
          $Res Function(_$PersonalIdChangedImpl) then) =
      __$$PersonalIdChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$PersonalIdChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$PersonalIdChangedImpl>
    implements _$$PersonalIdChangedImplCopyWith<$Res> {
  __$$PersonalIdChangedImplCopyWithImpl(_$PersonalIdChangedImpl _value,
      $Res Function(_$PersonalIdChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$PersonalIdChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$PersonalIdChangedImpl implements _PersonalIdChanged {
  const _$PersonalIdChangedImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'AddClientEvent.personalIdChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PersonalIdChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PersonalIdChangedImplCopyWith<_$PersonalIdChangedImpl> get copyWith =>
      __$$PersonalIdChangedImplCopyWithImpl<_$PersonalIdChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return personalIdChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return personalIdChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (personalIdChanged != null) {
      return personalIdChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return personalIdChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return personalIdChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (personalIdChanged != null) {
      return personalIdChanged(this);
    }
    return orElse();
  }
}

abstract class _PersonalIdChanged implements AddClientEvent {
  const factory _PersonalIdChanged(final String value) =
      _$PersonalIdChangedImpl;

  String get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PersonalIdChangedImplCopyWith<_$PersonalIdChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PhoneChangedImplCopyWith<$Res> {
  factory _$$PhoneChangedImplCopyWith(
          _$PhoneChangedImpl value, $Res Function(_$PhoneChangedImpl) then) =
      __$$PhoneChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$PhoneChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$PhoneChangedImpl>
    implements _$$PhoneChangedImplCopyWith<$Res> {
  __$$PhoneChangedImplCopyWithImpl(
      _$PhoneChangedImpl _value, $Res Function(_$PhoneChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$PhoneChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$PhoneChangedImpl implements PhoneChanged {
  const _$PhoneChangedImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'AddClientEvent.phoneChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhoneChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhoneChangedImplCopyWith<_$PhoneChangedImpl> get copyWith =>
      __$$PhoneChangedImplCopyWithImpl<_$PhoneChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return phoneChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return phoneChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (phoneChanged != null) {
      return phoneChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return phoneChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return phoneChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (phoneChanged != null) {
      return phoneChanged(this);
    }
    return orElse();
  }
}

abstract class PhoneChanged implements AddClientEvent {
  const factory PhoneChanged(final String value) = _$PhoneChangedImpl;

  String get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhoneChangedImplCopyWith<_$PhoneChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClientStatusChangedImplCopyWith<$Res> {
  factory _$$ClientStatusChangedImplCopyWith(_$ClientStatusChangedImpl value,
          $Res Function(_$ClientStatusChangedImpl) then) =
      __$$ClientStatusChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({ClientStatus value});
}

/// @nodoc
class __$$ClientStatusChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$ClientStatusChangedImpl>
    implements _$$ClientStatusChangedImplCopyWith<$Res> {
  __$$ClientStatusChangedImplCopyWithImpl(_$ClientStatusChangedImpl _value,
      $Res Function(_$ClientStatusChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ClientStatusChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as ClientStatus,
    ));
  }
}

/// @nodoc

class _$ClientStatusChangedImpl implements _ClientStatusChanged {
  const _$ClientStatusChangedImpl(this.value);

  @override
  final ClientStatus value;

  @override
  String toString() {
    return 'AddClientEvent.clientStatusChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ClientStatusChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ClientStatusChangedImplCopyWith<_$ClientStatusChangedImpl> get copyWith =>
      __$$ClientStatusChangedImplCopyWithImpl<_$ClientStatusChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return clientStatusChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return clientStatusChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (clientStatusChanged != null) {
      return clientStatusChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return clientStatusChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return clientStatusChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (clientStatusChanged != null) {
      return clientStatusChanged(this);
    }
    return orElse();
  }
}

abstract class _ClientStatusChanged implements AddClientEvent {
  const factory _ClientStatusChanged(final ClientStatus value) =
      _$ClientStatusChangedImpl;

  ClientStatus get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ClientStatusChangedImplCopyWith<_$ClientStatusChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PriorityLevelChangedImplCopyWith<$Res> {
  factory _$$PriorityLevelChangedImplCopyWith(_$PriorityLevelChangedImpl value,
          $Res Function(_$PriorityLevelChangedImpl) then) =
      __$$PriorityLevelChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PriorityLevel value});
}

/// @nodoc
class __$$PriorityLevelChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$PriorityLevelChangedImpl>
    implements _$$PriorityLevelChangedImplCopyWith<$Res> {
  __$$PriorityLevelChangedImplCopyWithImpl(_$PriorityLevelChangedImpl _value,
      $Res Function(_$PriorityLevelChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$PriorityLevelChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as PriorityLevel,
    ));
  }
}

/// @nodoc

class _$PriorityLevelChangedImpl implements _PriorityLevelChanged {
  const _$PriorityLevelChangedImpl(this.value);

  @override
  final PriorityLevel value;

  @override
  String toString() {
    return 'AddClientEvent.priorityLevelChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PriorityLevelChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PriorityLevelChangedImplCopyWith<_$PriorityLevelChangedImpl>
      get copyWith =>
          __$$PriorityLevelChangedImplCopyWithImpl<_$PriorityLevelChangedImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return priorityLevelChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return priorityLevelChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (priorityLevelChanged != null) {
      return priorityLevelChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return priorityLevelChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return priorityLevelChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (priorityLevelChanged != null) {
      return priorityLevelChanged(this);
    }
    return orElse();
  }
}

abstract class _PriorityLevelChanged implements AddClientEvent {
  const factory _PriorityLevelChanged(final PriorityLevel value) =
      _$PriorityLevelChangedImpl;

  PriorityLevel get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PriorityLevelChangedImplCopyWith<_$PriorityLevelChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DescriptionChangedImplCopyWith<$Res> {
  factory _$$DescriptionChangedImplCopyWith(_$DescriptionChangedImpl value,
          $Res Function(_$DescriptionChangedImpl) then) =
      __$$DescriptionChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$DescriptionChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$DescriptionChangedImpl>
    implements _$$DescriptionChangedImplCopyWith<$Res> {
  __$$DescriptionChangedImplCopyWithImpl(_$DescriptionChangedImpl _value,
      $Res Function(_$DescriptionChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$DescriptionChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DescriptionChangedImpl implements _DescriptionChanged {
  const _$DescriptionChangedImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'AddClientEvent.descriptionChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DescriptionChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DescriptionChangedImplCopyWith<_$DescriptionChangedImpl> get copyWith =>
      __$$DescriptionChangedImplCopyWithImpl<_$DescriptionChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return descriptionChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return descriptionChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (descriptionChanged != null) {
      return descriptionChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return descriptionChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return descriptionChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (descriptionChanged != null) {
      return descriptionChanged(this);
    }
    return orElse();
  }
}

abstract class _DescriptionChanged implements AddClientEvent {
  const factory _DescriptionChanged(final String value) =
      _$DescriptionChangedImpl;

  String get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DescriptionChangedImplCopyWith<_$DescriptionChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SocialLinksChangedImplCopyWith<$Res> {
  factory _$$SocialLinksChangedImplCopyWith(_$SocialLinksChangedImpl value,
          $Res Function(_$SocialLinksChangedImpl) then) =
      __$$SocialLinksChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<SocialMediaLink> value});
}

/// @nodoc
class __$$SocialLinksChangedImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$SocialLinksChangedImpl>
    implements _$$SocialLinksChangedImplCopyWith<$Res> {
  __$$SocialLinksChangedImplCopyWithImpl(_$SocialLinksChangedImpl _value,
      $Res Function(_$SocialLinksChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$SocialLinksChangedImpl(
      null == value
          ? _value._value
          : value // ignore: cast_nullable_to_non_nullable
              as List<SocialMediaLink>,
    ));
  }
}

/// @nodoc

class _$SocialLinksChangedImpl implements _SocialLinksChanged {
  const _$SocialLinksChangedImpl(final List<SocialMediaLink> value)
      : _value = value;

  final List<SocialMediaLink> _value;
  @override
  List<SocialMediaLink> get value {
    if (_value is EqualUnmodifiableListView) return _value;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_value);
  }

  @override
  String toString() {
    return 'AddClientEvent.socialLinksChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SocialLinksChangedImpl &&
            const DeepCollectionEquality().equals(other._value, _value));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_value));

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SocialLinksChangedImplCopyWith<_$SocialLinksChangedImpl> get copyWith =>
      __$$SocialLinksChangedImplCopyWithImpl<_$SocialLinksChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return socialLinksChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return socialLinksChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (socialLinksChanged != null) {
      return socialLinksChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return socialLinksChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return socialLinksChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (socialLinksChanged != null) {
      return socialLinksChanged(this);
    }
    return orElse();
  }
}

abstract class _SocialLinksChanged implements AddClientEvent {
  const factory _SocialLinksChanged(final List<SocialMediaLink> value) =
      _$SocialLinksChangedImpl;

  List<SocialMediaLink> get value;

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SocialLinksChangedImplCopyWith<_$SocialLinksChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SaveClientImplCopyWith<$Res> {
  factory _$$SaveClientImplCopyWith(
          _$SaveClientImpl value, $Res Function(_$SaveClientImpl) then) =
      __$$SaveClientImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SaveClientImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$SaveClientImpl>
    implements _$$SaveClientImplCopyWith<$Res> {
  __$$SaveClientImplCopyWithImpl(
      _$SaveClientImpl _value, $Res Function(_$SaveClientImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SaveClientImpl implements _SaveClient {
  const _$SaveClientImpl();

  @override
  String toString() {
    return 'AddClientEvent.save()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SaveClientImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return save();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return save?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (save != null) {
      return save();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return save(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return save?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (save != null) {
      return save(this);
    }
    return orElse();
  }
}

abstract class _SaveClient implements AddClientEvent {
  const factory _SaveClient() = _$SaveClientImpl;
}

/// @nodoc
abstract class _$$UpdateClientImplCopyWith<$Res> {
  factory _$$UpdateClientImplCopyWith(
          _$UpdateClientImpl value, $Res Function(_$UpdateClientImpl) then) =
      __$$UpdateClientImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdateClientImplCopyWithImpl<$Res>
    extends _$AddClientEventCopyWithImpl<$Res, _$UpdateClientImpl>
    implements _$$UpdateClientImplCopyWith<$Res> {
  __$$UpdateClientImplCopyWithImpl(
      _$UpdateClientImpl _value, $Res Function(_$UpdateClientImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdateClientImpl implements _UpdateClient {
  const _$UpdateClientImpl();

  @override
  String toString() {
    return 'AddClientEvent.update()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UpdateClientImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(String value) companyNameChanged,
    required TResult Function(String value) nameChanged,
    required TResult Function(String value) dateOfBirthChanged,
    required TResult Function(BusinessSector value) businessSectorChanged,
    required TResult Function(String value) companyIdChanged,
    required TResult Function(String value) personalIdChanged,
    required TResult Function(String value) phoneChanged,
    required TResult Function(ClientStatus value) clientStatusChanged,
    required TResult Function(PriorityLevel value) priorityLevelChanged,
    required TResult Function(String value) descriptionChanged,
    required TResult Function(List<SocialMediaLink> value) socialLinksChanged,
    required TResult Function() save,
    required TResult Function() update,
  }) {
    return update();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(String value)? companyNameChanged,
    TResult? Function(String value)? nameChanged,
    TResult? Function(String value)? dateOfBirthChanged,
    TResult? Function(BusinessSector value)? businessSectorChanged,
    TResult? Function(String value)? companyIdChanged,
    TResult? Function(String value)? personalIdChanged,
    TResult? Function(String value)? phoneChanged,
    TResult? Function(ClientStatus value)? clientStatusChanged,
    TResult? Function(PriorityLevel value)? priorityLevelChanged,
    TResult? Function(String value)? descriptionChanged,
    TResult? Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult? Function()? save,
    TResult? Function()? update,
  }) {
    return update?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(String value)? companyNameChanged,
    TResult Function(String value)? nameChanged,
    TResult Function(String value)? dateOfBirthChanged,
    TResult Function(BusinessSector value)? businessSectorChanged,
    TResult Function(String value)? companyIdChanged,
    TResult Function(String value)? personalIdChanged,
    TResult Function(String value)? phoneChanged,
    TResult Function(ClientStatus value)? clientStatusChanged,
    TResult Function(PriorityLevel value)? priorityLevelChanged,
    TResult Function(String value)? descriptionChanged,
    TResult Function(List<SocialMediaLink> value)? socialLinksChanged,
    TResult Function()? save,
    TResult Function()? update,
    required TResult orElse(),
  }) {
    if (update != null) {
      return update();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Load value) load,
    required TResult Function(_CompanyNameChanged value) companyNameChanged,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DateOfBirthChanged value) dateOfBirthChanged,
    required TResult Function(_BusinessSectorChanged value)
        businessSectorChanged,
    required TResult Function(_CompanyIdChanged value) companyIdChanged,
    required TResult Function(_PersonalIdChanged value) personalIdChanged,
    required TResult Function(PhoneChanged value) phoneChanged,
    required TResult Function(_ClientStatusChanged value) clientStatusChanged,
    required TResult Function(_PriorityLevelChanged value) priorityLevelChanged,
    required TResult Function(_DescriptionChanged value) descriptionChanged,
    required TResult Function(_SocialLinksChanged value) socialLinksChanged,
    required TResult Function(_SaveClient value) save,
    required TResult Function(_UpdateClient value) update,
  }) {
    return update(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Load value)? load,
    TResult? Function(_CompanyNameChanged value)? companyNameChanged,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult? Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult? Function(_CompanyIdChanged value)? companyIdChanged,
    TResult? Function(_PersonalIdChanged value)? personalIdChanged,
    TResult? Function(PhoneChanged value)? phoneChanged,
    TResult? Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult? Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult? Function(_DescriptionChanged value)? descriptionChanged,
    TResult? Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult? Function(_SaveClient value)? save,
    TResult? Function(_UpdateClient value)? update,
  }) {
    return update?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Load value)? load,
    TResult Function(_CompanyNameChanged value)? companyNameChanged,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DateOfBirthChanged value)? dateOfBirthChanged,
    TResult Function(_BusinessSectorChanged value)? businessSectorChanged,
    TResult Function(_CompanyIdChanged value)? companyIdChanged,
    TResult Function(_PersonalIdChanged value)? personalIdChanged,
    TResult Function(PhoneChanged value)? phoneChanged,
    TResult Function(_ClientStatusChanged value)? clientStatusChanged,
    TResult Function(_PriorityLevelChanged value)? priorityLevelChanged,
    TResult Function(_DescriptionChanged value)? descriptionChanged,
    TResult Function(_SocialLinksChanged value)? socialLinksChanged,
    TResult Function(_SaveClient value)? save,
    TResult Function(_UpdateClient value)? update,
    required TResult orElse(),
  }) {
    if (update != null) {
      return update(this);
    }
    return orElse();
  }
}

abstract class _UpdateClient implements AddClientEvent {
  const factory _UpdateClient() = _$UpdateClientImpl;
}
