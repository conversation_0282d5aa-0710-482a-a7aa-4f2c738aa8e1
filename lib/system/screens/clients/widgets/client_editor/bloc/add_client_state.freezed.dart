// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_client_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AddClientState {
  Status get status => throw _privateConstructorUsedError;
  bool get isUpdateMode =>
      throw _privateConstructorUsedError; // True when updating an existing client.
  String get companyName => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get dateOfBirth => throw _privateConstructorUsedError;
  BusinessSector get businessSector => throw _privateConstructorUsedError;
  String get companyId => throw _privateConstructorUsedError;
  String get personalId => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  ClientStatus get clientStatus => throw _privateConstructorUsedError;
  PriorityLevel get priorityLevel => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  List<SocialMediaLink> get socialLinks => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Create a copy of AddClientState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddClientStateCopyWith<AddClientState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddClientStateCopyWith<$Res> {
  factory $AddClientStateCopyWith(
          AddClientState value, $Res Function(AddClientState) then) =
      _$AddClientStateCopyWithImpl<$Res, AddClientState>;
  @useResult
  $Res call(
      {Status status,
      bool isUpdateMode,
      String companyName,
      String name,
      String dateOfBirth,
      BusinessSector businessSector,
      String companyId,
      String personalId,
      String phone,
      ClientStatus clientStatus,
      PriorityLevel priorityLevel,
      String description,
      List<SocialMediaLink> socialLinks,
      String? errorMessage});
}

/// @nodoc
class _$AddClientStateCopyWithImpl<$Res, $Val extends AddClientState>
    implements $AddClientStateCopyWith<$Res> {
  _$AddClientStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddClientState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? isUpdateMode = null,
    Object? companyName = null,
    Object? name = null,
    Object? dateOfBirth = null,
    Object? businessSector = null,
    Object? companyId = null,
    Object? personalId = null,
    Object? phone = null,
    Object? clientStatus = null,
    Object? priorityLevel = null,
    Object? description = null,
    Object? socialLinks = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as Status,
      isUpdateMode: null == isUpdateMode
          ? _value.isUpdateMode
          : isUpdateMode // ignore: cast_nullable_to_non_nullable
              as bool,
      companyName: null == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      dateOfBirth: null == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as String,
      businessSector: null == businessSector
          ? _value.businessSector
          : businessSector // ignore: cast_nullable_to_non_nullable
              as BusinessSector,
      companyId: null == companyId
          ? _value.companyId
          : companyId // ignore: cast_nullable_to_non_nullable
              as String,
      personalId: null == personalId
          ? _value.personalId
          : personalId // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      clientStatus: null == clientStatus
          ? _value.clientStatus
          : clientStatus // ignore: cast_nullable_to_non_nullable
              as ClientStatus,
      priorityLevel: null == priorityLevel
          ? _value.priorityLevel
          : priorityLevel // ignore: cast_nullable_to_non_nullable
              as PriorityLevel,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      socialLinks: null == socialLinks
          ? _value.socialLinks
          : socialLinks // ignore: cast_nullable_to_non_nullable
              as List<SocialMediaLink>,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddClientStateImplCopyWith<$Res>
    implements $AddClientStateCopyWith<$Res> {
  factory _$$AddClientStateImplCopyWith(_$AddClientStateImpl value,
          $Res Function(_$AddClientStateImpl) then) =
      __$$AddClientStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Status status,
      bool isUpdateMode,
      String companyName,
      String name,
      String dateOfBirth,
      BusinessSector businessSector,
      String companyId,
      String personalId,
      String phone,
      ClientStatus clientStatus,
      PriorityLevel priorityLevel,
      String description,
      List<SocialMediaLink> socialLinks,
      String? errorMessage});
}

/// @nodoc
class __$$AddClientStateImplCopyWithImpl<$Res>
    extends _$AddClientStateCopyWithImpl<$Res, _$AddClientStateImpl>
    implements _$$AddClientStateImplCopyWith<$Res> {
  __$$AddClientStateImplCopyWithImpl(
      _$AddClientStateImpl _value, $Res Function(_$AddClientStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddClientState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? isUpdateMode = null,
    Object? companyName = null,
    Object? name = null,
    Object? dateOfBirth = null,
    Object? businessSector = null,
    Object? companyId = null,
    Object? personalId = null,
    Object? phone = null,
    Object? clientStatus = null,
    Object? priorityLevel = null,
    Object? description = null,
    Object? socialLinks = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$AddClientStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as Status,
      isUpdateMode: null == isUpdateMode
          ? _value.isUpdateMode
          : isUpdateMode // ignore: cast_nullable_to_non_nullable
              as bool,
      companyName: null == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      dateOfBirth: null == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as String,
      businessSector: null == businessSector
          ? _value.businessSector
          : businessSector // ignore: cast_nullable_to_non_nullable
              as BusinessSector,
      companyId: null == companyId
          ? _value.companyId
          : companyId // ignore: cast_nullable_to_non_nullable
              as String,
      personalId: null == personalId
          ? _value.personalId
          : personalId // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      clientStatus: null == clientStatus
          ? _value.clientStatus
          : clientStatus // ignore: cast_nullable_to_non_nullable
              as ClientStatus,
      priorityLevel: null == priorityLevel
          ? _value.priorityLevel
          : priorityLevel // ignore: cast_nullable_to_non_nullable
              as PriorityLevel,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      socialLinks: null == socialLinks
          ? _value._socialLinks
          : socialLinks // ignore: cast_nullable_to_non_nullable
              as List<SocialMediaLink>,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AddClientStateImpl implements _AddClientState {
  _$AddClientStateImpl(
      {this.status = Status.initial,
      this.isUpdateMode = false,
      this.companyName = '',
      this.name = '',
      this.dateOfBirth = '',
      this.businessSector = BusinessSector.unknown,
      this.companyId = '',
      this.personalId = '',
      this.phone = '',
      this.clientStatus = ClientStatus.inactive,
      this.priorityLevel = PriorityLevel.lowPriority,
      this.description = '',
      final List<SocialMediaLink> socialLinks = const [],
      this.errorMessage})
      : _socialLinks = socialLinks;

  @override
  @JsonKey()
  final Status status;
  @override
  @JsonKey()
  final bool isUpdateMode;
// True when updating an existing client.
  @override
  @JsonKey()
  final String companyName;
  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String dateOfBirth;
  @override
  @JsonKey()
  final BusinessSector businessSector;
  @override
  @JsonKey()
  final String companyId;
  @override
  @JsonKey()
  final String personalId;
  @override
  @JsonKey()
  final String phone;
  @override
  @JsonKey()
  final ClientStatus clientStatus;
  @override
  @JsonKey()
  final PriorityLevel priorityLevel;
  @override
  @JsonKey()
  final String description;
  final List<SocialMediaLink> _socialLinks;
  @override
  @JsonKey()
  List<SocialMediaLink> get socialLinks {
    if (_socialLinks is EqualUnmodifiableListView) return _socialLinks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_socialLinks);
  }

  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'AddClientState(status: $status, isUpdateMode: $isUpdateMode, companyName: $companyName, name: $name, dateOfBirth: $dateOfBirth, businessSector: $businessSector, companyId: $companyId, personalId: $personalId, phone: $phone, clientStatus: $clientStatus, priorityLevel: $priorityLevel, description: $description, socialLinks: $socialLinks, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddClientStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isUpdateMode, isUpdateMode) ||
                other.isUpdateMode == isUpdateMode) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.businessSector, businessSector) ||
                other.businessSector == businessSector) &&
            (identical(other.companyId, companyId) ||
                other.companyId == companyId) &&
            (identical(other.personalId, personalId) ||
                other.personalId == personalId) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.clientStatus, clientStatus) ||
                other.clientStatus == clientStatus) &&
            (identical(other.priorityLevel, priorityLevel) ||
                other.priorityLevel == priorityLevel) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality()
                .equals(other._socialLinks, _socialLinks) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      status,
      isUpdateMode,
      companyName,
      name,
      dateOfBirth,
      businessSector,
      companyId,
      personalId,
      phone,
      clientStatus,
      priorityLevel,
      description,
      const DeepCollectionEquality().hash(_socialLinks),
      errorMessage);

  /// Create a copy of AddClientState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddClientStateImplCopyWith<_$AddClientStateImpl> get copyWith =>
      __$$AddClientStateImplCopyWithImpl<_$AddClientStateImpl>(
          this, _$identity);
}

abstract class _AddClientState implements AddClientState {
  factory _AddClientState(
      {final Status status,
      final bool isUpdateMode,
      final String companyName,
      final String name,
      final String dateOfBirth,
      final BusinessSector businessSector,
      final String companyId,
      final String personalId,
      final String phone,
      final ClientStatus clientStatus,
      final PriorityLevel priorityLevel,
      final String description,
      final List<SocialMediaLink> socialLinks,
      final String? errorMessage}) = _$AddClientStateImpl;

  @override
  Status get status;
  @override
  bool get isUpdateMode; // True when updating an existing client.
  @override
  String get companyName;
  @override
  String get name;
  @override
  String get dateOfBirth;
  @override
  BusinessSector get businessSector;
  @override
  String get companyId;
  @override
  String get personalId;
  @override
  String get phone;
  @override
  ClientStatus get clientStatus;
  @override
  PriorityLevel get priorityLevel;
  @override
  String get description;
  @override
  List<SocialMediaLink> get socialLinks;
  @override
  String? get errorMessage;

  /// Create a copy of AddClientState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddClientStateImplCopyWith<_$AddClientStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
