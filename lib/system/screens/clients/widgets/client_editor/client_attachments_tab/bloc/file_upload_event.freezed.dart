// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'file_upload_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FileUploadEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAllSections,
    required TResult Function(FileType fileType) loadSection,
    required TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)
        fileDropped,
    required TResult Function(FileType fileType, picker.PlatformFile file)
        filePicked,
    required TResult Function(FileType fileType) dragEntered,
    required TResult Function(FileType fileType) dragLeft,
    required TResult Function(FileType fileType, String fileId) fileRemoved,
    required TResult Function(FileType fileType) errorCleared,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAllSections,
    TResult? Function(FileType fileType)? loadSection,
    TResult? Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult? Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult? Function(FileType fileType)? dragEntered,
    TResult? Function(FileType fileType)? dragLeft,
    TResult? Function(FileType fileType, String fileId)? fileRemoved,
    TResult? Function(FileType fileType)? errorCleared,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAllSections,
    TResult Function(FileType fileType)? loadSection,
    TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult Function(FileType fileType)? dragEntered,
    TResult Function(FileType fileType)? dragLeft,
    TResult Function(FileType fileType, String fileId)? fileRemoved,
    TResult Function(FileType fileType)? errorCleared,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllSections value) loadAllSections,
    required TResult Function(_LoadSection value) loadSection,
    required TResult Function(_FileDropped value) fileDropped,
    required TResult Function(_FilePicked value) filePicked,
    required TResult Function(_DragEntered value) dragEntered,
    required TResult Function(_DragLeft value) dragLeft,
    required TResult Function(_FileRemoved value) fileRemoved,
    required TResult Function(_ErrorCleared value) errorCleared,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllSections value)? loadAllSections,
    TResult? Function(_LoadSection value)? loadSection,
    TResult? Function(_FileDropped value)? fileDropped,
    TResult? Function(_FilePicked value)? filePicked,
    TResult? Function(_DragEntered value)? dragEntered,
    TResult? Function(_DragLeft value)? dragLeft,
    TResult? Function(_FileRemoved value)? fileRemoved,
    TResult? Function(_ErrorCleared value)? errorCleared,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllSections value)? loadAllSections,
    TResult Function(_LoadSection value)? loadSection,
    TResult Function(_FileDropped value)? fileDropped,
    TResult Function(_FilePicked value)? filePicked,
    TResult Function(_DragEntered value)? dragEntered,
    TResult Function(_DragLeft value)? dragLeft,
    TResult Function(_FileRemoved value)? fileRemoved,
    TResult Function(_ErrorCleared value)? errorCleared,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FileUploadEventCopyWith<$Res> {
  factory $FileUploadEventCopyWith(
          FileUploadEvent value, $Res Function(FileUploadEvent) then) =
      _$FileUploadEventCopyWithImpl<$Res, FileUploadEvent>;
}

/// @nodoc
class _$FileUploadEventCopyWithImpl<$Res, $Val extends FileUploadEvent>
    implements $FileUploadEventCopyWith<$Res> {
  _$FileUploadEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadAllSectionsImplCopyWith<$Res> {
  factory _$$LoadAllSectionsImplCopyWith(_$LoadAllSectionsImpl value,
          $Res Function(_$LoadAllSectionsImpl) then) =
      __$$LoadAllSectionsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadAllSectionsImplCopyWithImpl<$Res>
    extends _$FileUploadEventCopyWithImpl<$Res, _$LoadAllSectionsImpl>
    implements _$$LoadAllSectionsImplCopyWith<$Res> {
  __$$LoadAllSectionsImplCopyWithImpl(
      _$LoadAllSectionsImpl _value, $Res Function(_$LoadAllSectionsImpl) _then)
      : super(_value, _then);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadAllSectionsImpl implements _LoadAllSections {
  const _$LoadAllSectionsImpl();

  @override
  String toString() {
    return 'FileUploadEvent.loadAllSections()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadAllSectionsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAllSections,
    required TResult Function(FileType fileType) loadSection,
    required TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)
        fileDropped,
    required TResult Function(FileType fileType, picker.PlatformFile file)
        filePicked,
    required TResult Function(FileType fileType) dragEntered,
    required TResult Function(FileType fileType) dragLeft,
    required TResult Function(FileType fileType, String fileId) fileRemoved,
    required TResult Function(FileType fileType) errorCleared,
  }) {
    return loadAllSections();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAllSections,
    TResult? Function(FileType fileType)? loadSection,
    TResult? Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult? Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult? Function(FileType fileType)? dragEntered,
    TResult? Function(FileType fileType)? dragLeft,
    TResult? Function(FileType fileType, String fileId)? fileRemoved,
    TResult? Function(FileType fileType)? errorCleared,
  }) {
    return loadAllSections?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAllSections,
    TResult Function(FileType fileType)? loadSection,
    TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult Function(FileType fileType)? dragEntered,
    TResult Function(FileType fileType)? dragLeft,
    TResult Function(FileType fileType, String fileId)? fileRemoved,
    TResult Function(FileType fileType)? errorCleared,
    required TResult orElse(),
  }) {
    if (loadAllSections != null) {
      return loadAllSections();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllSections value) loadAllSections,
    required TResult Function(_LoadSection value) loadSection,
    required TResult Function(_FileDropped value) fileDropped,
    required TResult Function(_FilePicked value) filePicked,
    required TResult Function(_DragEntered value) dragEntered,
    required TResult Function(_DragLeft value) dragLeft,
    required TResult Function(_FileRemoved value) fileRemoved,
    required TResult Function(_ErrorCleared value) errorCleared,
  }) {
    return loadAllSections(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllSections value)? loadAllSections,
    TResult? Function(_LoadSection value)? loadSection,
    TResult? Function(_FileDropped value)? fileDropped,
    TResult? Function(_FilePicked value)? filePicked,
    TResult? Function(_DragEntered value)? dragEntered,
    TResult? Function(_DragLeft value)? dragLeft,
    TResult? Function(_FileRemoved value)? fileRemoved,
    TResult? Function(_ErrorCleared value)? errorCleared,
  }) {
    return loadAllSections?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllSections value)? loadAllSections,
    TResult Function(_LoadSection value)? loadSection,
    TResult Function(_FileDropped value)? fileDropped,
    TResult Function(_FilePicked value)? filePicked,
    TResult Function(_DragEntered value)? dragEntered,
    TResult Function(_DragLeft value)? dragLeft,
    TResult Function(_FileRemoved value)? fileRemoved,
    TResult Function(_ErrorCleared value)? errorCleared,
    required TResult orElse(),
  }) {
    if (loadAllSections != null) {
      return loadAllSections(this);
    }
    return orElse();
  }
}

abstract class _LoadAllSections implements FileUploadEvent {
  const factory _LoadAllSections() = _$LoadAllSectionsImpl;
}

/// @nodoc
abstract class _$$LoadSectionImplCopyWith<$Res> {
  factory _$$LoadSectionImplCopyWith(
          _$LoadSectionImpl value, $Res Function(_$LoadSectionImpl) then) =
      __$$LoadSectionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({FileType fileType});
}

/// @nodoc
class __$$LoadSectionImplCopyWithImpl<$Res>
    extends _$FileUploadEventCopyWithImpl<$Res, _$LoadSectionImpl>
    implements _$$LoadSectionImplCopyWith<$Res> {
  __$$LoadSectionImplCopyWithImpl(
      _$LoadSectionImpl _value, $Res Function(_$LoadSectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileType = null,
  }) {
    return _then(_$LoadSectionImpl(
      null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as FileType,
    ));
  }
}

/// @nodoc

class _$LoadSectionImpl implements _LoadSection {
  const _$LoadSectionImpl(this.fileType);

  @override
  final FileType fileType;

  @override
  String toString() {
    return 'FileUploadEvent.loadSection(fileType: $fileType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadSectionImpl &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fileType);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadSectionImplCopyWith<_$LoadSectionImpl> get copyWith =>
      __$$LoadSectionImplCopyWithImpl<_$LoadSectionImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAllSections,
    required TResult Function(FileType fileType) loadSection,
    required TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)
        fileDropped,
    required TResult Function(FileType fileType, picker.PlatformFile file)
        filePicked,
    required TResult Function(FileType fileType) dragEntered,
    required TResult Function(FileType fileType) dragLeft,
    required TResult Function(FileType fileType, String fileId) fileRemoved,
    required TResult Function(FileType fileType) errorCleared,
  }) {
    return loadSection(fileType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAllSections,
    TResult? Function(FileType fileType)? loadSection,
    TResult? Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult? Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult? Function(FileType fileType)? dragEntered,
    TResult? Function(FileType fileType)? dragLeft,
    TResult? Function(FileType fileType, String fileId)? fileRemoved,
    TResult? Function(FileType fileType)? errorCleared,
  }) {
    return loadSection?.call(fileType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAllSections,
    TResult Function(FileType fileType)? loadSection,
    TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult Function(FileType fileType)? dragEntered,
    TResult Function(FileType fileType)? dragLeft,
    TResult Function(FileType fileType, String fileId)? fileRemoved,
    TResult Function(FileType fileType)? errorCleared,
    required TResult orElse(),
  }) {
    if (loadSection != null) {
      return loadSection(fileType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllSections value) loadAllSections,
    required TResult Function(_LoadSection value) loadSection,
    required TResult Function(_FileDropped value) fileDropped,
    required TResult Function(_FilePicked value) filePicked,
    required TResult Function(_DragEntered value) dragEntered,
    required TResult Function(_DragLeft value) dragLeft,
    required TResult Function(_FileRemoved value) fileRemoved,
    required TResult Function(_ErrorCleared value) errorCleared,
  }) {
    return loadSection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllSections value)? loadAllSections,
    TResult? Function(_LoadSection value)? loadSection,
    TResult? Function(_FileDropped value)? fileDropped,
    TResult? Function(_FilePicked value)? filePicked,
    TResult? Function(_DragEntered value)? dragEntered,
    TResult? Function(_DragLeft value)? dragLeft,
    TResult? Function(_FileRemoved value)? fileRemoved,
    TResult? Function(_ErrorCleared value)? errorCleared,
  }) {
    return loadSection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllSections value)? loadAllSections,
    TResult Function(_LoadSection value)? loadSection,
    TResult Function(_FileDropped value)? fileDropped,
    TResult Function(_FilePicked value)? filePicked,
    TResult Function(_DragEntered value)? dragEntered,
    TResult Function(_DragLeft value)? dragLeft,
    TResult Function(_FileRemoved value)? fileRemoved,
    TResult Function(_ErrorCleared value)? errorCleared,
    required TResult orElse(),
  }) {
    if (loadSection != null) {
      return loadSection(this);
    }
    return orElse();
  }
}

abstract class _LoadSection implements FileUploadEvent {
  const factory _LoadSection(final FileType fileType) = _$LoadSectionImpl;

  FileType get fileType;

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadSectionImplCopyWith<_$LoadSectionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FileDroppedImplCopyWith<$Res> {
  factory _$$FileDroppedImplCopyWith(
          _$FileDroppedImpl value, $Res Function(_$FileDroppedImpl) then) =
      __$$FileDroppedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {FileType fileType,
      DropzoneFileInterface file,
      DropzoneViewController controller});
}

/// @nodoc
class __$$FileDroppedImplCopyWithImpl<$Res>
    extends _$FileUploadEventCopyWithImpl<$Res, _$FileDroppedImpl>
    implements _$$FileDroppedImplCopyWith<$Res> {
  __$$FileDroppedImplCopyWithImpl(
      _$FileDroppedImpl _value, $Res Function(_$FileDroppedImpl) _then)
      : super(_value, _then);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileType = null,
    Object? file = null,
    Object? controller = null,
  }) {
    return _then(_$FileDroppedImpl(
      fileType: null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as FileType,
      file: null == file
          ? _value.file
          : file // ignore: cast_nullable_to_non_nullable
              as DropzoneFileInterface,
      controller: null == controller
          ? _value.controller
          : controller // ignore: cast_nullable_to_non_nullable
              as DropzoneViewController,
    ));
  }
}

/// @nodoc

class _$FileDroppedImpl implements _FileDropped {
  const _$FileDroppedImpl(
      {required this.fileType, required this.file, required this.controller});

  @override
  final FileType fileType;
  @override
  final DropzoneFileInterface file;
  @override
  final DropzoneViewController controller;

  @override
  String toString() {
    return 'FileUploadEvent.fileDropped(fileType: $fileType, file: $file, controller: $controller)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FileDroppedImpl &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType) &&
            (identical(other.file, file) || other.file == file) &&
            (identical(other.controller, controller) ||
                other.controller == controller));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fileType, file, controller);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FileDroppedImplCopyWith<_$FileDroppedImpl> get copyWith =>
      __$$FileDroppedImplCopyWithImpl<_$FileDroppedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAllSections,
    required TResult Function(FileType fileType) loadSection,
    required TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)
        fileDropped,
    required TResult Function(FileType fileType, picker.PlatformFile file)
        filePicked,
    required TResult Function(FileType fileType) dragEntered,
    required TResult Function(FileType fileType) dragLeft,
    required TResult Function(FileType fileType, String fileId) fileRemoved,
    required TResult Function(FileType fileType) errorCleared,
  }) {
    return fileDropped(fileType, file, controller);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAllSections,
    TResult? Function(FileType fileType)? loadSection,
    TResult? Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult? Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult? Function(FileType fileType)? dragEntered,
    TResult? Function(FileType fileType)? dragLeft,
    TResult? Function(FileType fileType, String fileId)? fileRemoved,
    TResult? Function(FileType fileType)? errorCleared,
  }) {
    return fileDropped?.call(fileType, file, controller);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAllSections,
    TResult Function(FileType fileType)? loadSection,
    TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult Function(FileType fileType)? dragEntered,
    TResult Function(FileType fileType)? dragLeft,
    TResult Function(FileType fileType, String fileId)? fileRemoved,
    TResult Function(FileType fileType)? errorCleared,
    required TResult orElse(),
  }) {
    if (fileDropped != null) {
      return fileDropped(fileType, file, controller);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllSections value) loadAllSections,
    required TResult Function(_LoadSection value) loadSection,
    required TResult Function(_FileDropped value) fileDropped,
    required TResult Function(_FilePicked value) filePicked,
    required TResult Function(_DragEntered value) dragEntered,
    required TResult Function(_DragLeft value) dragLeft,
    required TResult Function(_FileRemoved value) fileRemoved,
    required TResult Function(_ErrorCleared value) errorCleared,
  }) {
    return fileDropped(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllSections value)? loadAllSections,
    TResult? Function(_LoadSection value)? loadSection,
    TResult? Function(_FileDropped value)? fileDropped,
    TResult? Function(_FilePicked value)? filePicked,
    TResult? Function(_DragEntered value)? dragEntered,
    TResult? Function(_DragLeft value)? dragLeft,
    TResult? Function(_FileRemoved value)? fileRemoved,
    TResult? Function(_ErrorCleared value)? errorCleared,
  }) {
    return fileDropped?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllSections value)? loadAllSections,
    TResult Function(_LoadSection value)? loadSection,
    TResult Function(_FileDropped value)? fileDropped,
    TResult Function(_FilePicked value)? filePicked,
    TResult Function(_DragEntered value)? dragEntered,
    TResult Function(_DragLeft value)? dragLeft,
    TResult Function(_FileRemoved value)? fileRemoved,
    TResult Function(_ErrorCleared value)? errorCleared,
    required TResult orElse(),
  }) {
    if (fileDropped != null) {
      return fileDropped(this);
    }
    return orElse();
  }
}

abstract class _FileDropped implements FileUploadEvent {
  const factory _FileDropped(
      {required final FileType fileType,
      required final DropzoneFileInterface file,
      required final DropzoneViewController controller}) = _$FileDroppedImpl;

  FileType get fileType;
  DropzoneFileInterface get file;
  DropzoneViewController get controller;

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FileDroppedImplCopyWith<_$FileDroppedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FilePickedImplCopyWith<$Res> {
  factory _$$FilePickedImplCopyWith(
          _$FilePickedImpl value, $Res Function(_$FilePickedImpl) then) =
      __$$FilePickedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({FileType fileType, picker.PlatformFile file});
}

/// @nodoc
class __$$FilePickedImplCopyWithImpl<$Res>
    extends _$FileUploadEventCopyWithImpl<$Res, _$FilePickedImpl>
    implements _$$FilePickedImplCopyWith<$Res> {
  __$$FilePickedImplCopyWithImpl(
      _$FilePickedImpl _value, $Res Function(_$FilePickedImpl) _then)
      : super(_value, _then);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileType = null,
    Object? file = null,
  }) {
    return _then(_$FilePickedImpl(
      fileType: null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as FileType,
      file: null == file
          ? _value.file
          : file // ignore: cast_nullable_to_non_nullable
              as picker.PlatformFile,
    ));
  }
}

/// @nodoc

class _$FilePickedImpl implements _FilePicked {
  const _$FilePickedImpl({required this.fileType, required this.file});

  @override
  final FileType fileType;
  @override
  final picker.PlatformFile file;

  @override
  String toString() {
    return 'FileUploadEvent.filePicked(fileType: $fileType, file: $file)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilePickedImpl &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType) &&
            (identical(other.file, file) || other.file == file));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fileType, file);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FilePickedImplCopyWith<_$FilePickedImpl> get copyWith =>
      __$$FilePickedImplCopyWithImpl<_$FilePickedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAllSections,
    required TResult Function(FileType fileType) loadSection,
    required TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)
        fileDropped,
    required TResult Function(FileType fileType, picker.PlatformFile file)
        filePicked,
    required TResult Function(FileType fileType) dragEntered,
    required TResult Function(FileType fileType) dragLeft,
    required TResult Function(FileType fileType, String fileId) fileRemoved,
    required TResult Function(FileType fileType) errorCleared,
  }) {
    return filePicked(fileType, file);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAllSections,
    TResult? Function(FileType fileType)? loadSection,
    TResult? Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult? Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult? Function(FileType fileType)? dragEntered,
    TResult? Function(FileType fileType)? dragLeft,
    TResult? Function(FileType fileType, String fileId)? fileRemoved,
    TResult? Function(FileType fileType)? errorCleared,
  }) {
    return filePicked?.call(fileType, file);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAllSections,
    TResult Function(FileType fileType)? loadSection,
    TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult Function(FileType fileType)? dragEntered,
    TResult Function(FileType fileType)? dragLeft,
    TResult Function(FileType fileType, String fileId)? fileRemoved,
    TResult Function(FileType fileType)? errorCleared,
    required TResult orElse(),
  }) {
    if (filePicked != null) {
      return filePicked(fileType, file);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllSections value) loadAllSections,
    required TResult Function(_LoadSection value) loadSection,
    required TResult Function(_FileDropped value) fileDropped,
    required TResult Function(_FilePicked value) filePicked,
    required TResult Function(_DragEntered value) dragEntered,
    required TResult Function(_DragLeft value) dragLeft,
    required TResult Function(_FileRemoved value) fileRemoved,
    required TResult Function(_ErrorCleared value) errorCleared,
  }) {
    return filePicked(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllSections value)? loadAllSections,
    TResult? Function(_LoadSection value)? loadSection,
    TResult? Function(_FileDropped value)? fileDropped,
    TResult? Function(_FilePicked value)? filePicked,
    TResult? Function(_DragEntered value)? dragEntered,
    TResult? Function(_DragLeft value)? dragLeft,
    TResult? Function(_FileRemoved value)? fileRemoved,
    TResult? Function(_ErrorCleared value)? errorCleared,
  }) {
    return filePicked?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllSections value)? loadAllSections,
    TResult Function(_LoadSection value)? loadSection,
    TResult Function(_FileDropped value)? fileDropped,
    TResult Function(_FilePicked value)? filePicked,
    TResult Function(_DragEntered value)? dragEntered,
    TResult Function(_DragLeft value)? dragLeft,
    TResult Function(_FileRemoved value)? fileRemoved,
    TResult Function(_ErrorCleared value)? errorCleared,
    required TResult orElse(),
  }) {
    if (filePicked != null) {
      return filePicked(this);
    }
    return orElse();
  }
}

abstract class _FilePicked implements FileUploadEvent {
  const factory _FilePicked(
      {required final FileType fileType,
      required final picker.PlatformFile file}) = _$FilePickedImpl;

  FileType get fileType;
  picker.PlatformFile get file;

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FilePickedImplCopyWith<_$FilePickedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DragEnteredImplCopyWith<$Res> {
  factory _$$DragEnteredImplCopyWith(
          _$DragEnteredImpl value, $Res Function(_$DragEnteredImpl) then) =
      __$$DragEnteredImplCopyWithImpl<$Res>;
  @useResult
  $Res call({FileType fileType});
}

/// @nodoc
class __$$DragEnteredImplCopyWithImpl<$Res>
    extends _$FileUploadEventCopyWithImpl<$Res, _$DragEnteredImpl>
    implements _$$DragEnteredImplCopyWith<$Res> {
  __$$DragEnteredImplCopyWithImpl(
      _$DragEnteredImpl _value, $Res Function(_$DragEnteredImpl) _then)
      : super(_value, _then);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileType = null,
  }) {
    return _then(_$DragEnteredImpl(
      null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as FileType,
    ));
  }
}

/// @nodoc

class _$DragEnteredImpl implements _DragEntered {
  const _$DragEnteredImpl(this.fileType);

  @override
  final FileType fileType;

  @override
  String toString() {
    return 'FileUploadEvent.dragEntered(fileType: $fileType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DragEnteredImpl &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fileType);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DragEnteredImplCopyWith<_$DragEnteredImpl> get copyWith =>
      __$$DragEnteredImplCopyWithImpl<_$DragEnteredImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAllSections,
    required TResult Function(FileType fileType) loadSection,
    required TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)
        fileDropped,
    required TResult Function(FileType fileType, picker.PlatformFile file)
        filePicked,
    required TResult Function(FileType fileType) dragEntered,
    required TResult Function(FileType fileType) dragLeft,
    required TResult Function(FileType fileType, String fileId) fileRemoved,
    required TResult Function(FileType fileType) errorCleared,
  }) {
    return dragEntered(fileType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAllSections,
    TResult? Function(FileType fileType)? loadSection,
    TResult? Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult? Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult? Function(FileType fileType)? dragEntered,
    TResult? Function(FileType fileType)? dragLeft,
    TResult? Function(FileType fileType, String fileId)? fileRemoved,
    TResult? Function(FileType fileType)? errorCleared,
  }) {
    return dragEntered?.call(fileType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAllSections,
    TResult Function(FileType fileType)? loadSection,
    TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult Function(FileType fileType)? dragEntered,
    TResult Function(FileType fileType)? dragLeft,
    TResult Function(FileType fileType, String fileId)? fileRemoved,
    TResult Function(FileType fileType)? errorCleared,
    required TResult orElse(),
  }) {
    if (dragEntered != null) {
      return dragEntered(fileType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllSections value) loadAllSections,
    required TResult Function(_LoadSection value) loadSection,
    required TResult Function(_FileDropped value) fileDropped,
    required TResult Function(_FilePicked value) filePicked,
    required TResult Function(_DragEntered value) dragEntered,
    required TResult Function(_DragLeft value) dragLeft,
    required TResult Function(_FileRemoved value) fileRemoved,
    required TResult Function(_ErrorCleared value) errorCleared,
  }) {
    return dragEntered(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllSections value)? loadAllSections,
    TResult? Function(_LoadSection value)? loadSection,
    TResult? Function(_FileDropped value)? fileDropped,
    TResult? Function(_FilePicked value)? filePicked,
    TResult? Function(_DragEntered value)? dragEntered,
    TResult? Function(_DragLeft value)? dragLeft,
    TResult? Function(_FileRemoved value)? fileRemoved,
    TResult? Function(_ErrorCleared value)? errorCleared,
  }) {
    return dragEntered?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllSections value)? loadAllSections,
    TResult Function(_LoadSection value)? loadSection,
    TResult Function(_FileDropped value)? fileDropped,
    TResult Function(_FilePicked value)? filePicked,
    TResult Function(_DragEntered value)? dragEntered,
    TResult Function(_DragLeft value)? dragLeft,
    TResult Function(_FileRemoved value)? fileRemoved,
    TResult Function(_ErrorCleared value)? errorCleared,
    required TResult orElse(),
  }) {
    if (dragEntered != null) {
      return dragEntered(this);
    }
    return orElse();
  }
}

abstract class _DragEntered implements FileUploadEvent {
  const factory _DragEntered(final FileType fileType) = _$DragEnteredImpl;

  FileType get fileType;

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DragEnteredImplCopyWith<_$DragEnteredImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DragLeftImplCopyWith<$Res> {
  factory _$$DragLeftImplCopyWith(
          _$DragLeftImpl value, $Res Function(_$DragLeftImpl) then) =
      __$$DragLeftImplCopyWithImpl<$Res>;
  @useResult
  $Res call({FileType fileType});
}

/// @nodoc
class __$$DragLeftImplCopyWithImpl<$Res>
    extends _$FileUploadEventCopyWithImpl<$Res, _$DragLeftImpl>
    implements _$$DragLeftImplCopyWith<$Res> {
  __$$DragLeftImplCopyWithImpl(
      _$DragLeftImpl _value, $Res Function(_$DragLeftImpl) _then)
      : super(_value, _then);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileType = null,
  }) {
    return _then(_$DragLeftImpl(
      null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as FileType,
    ));
  }
}

/// @nodoc

class _$DragLeftImpl implements _DragLeft {
  const _$DragLeftImpl(this.fileType);

  @override
  final FileType fileType;

  @override
  String toString() {
    return 'FileUploadEvent.dragLeft(fileType: $fileType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DragLeftImpl &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fileType);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DragLeftImplCopyWith<_$DragLeftImpl> get copyWith =>
      __$$DragLeftImplCopyWithImpl<_$DragLeftImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAllSections,
    required TResult Function(FileType fileType) loadSection,
    required TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)
        fileDropped,
    required TResult Function(FileType fileType, picker.PlatformFile file)
        filePicked,
    required TResult Function(FileType fileType) dragEntered,
    required TResult Function(FileType fileType) dragLeft,
    required TResult Function(FileType fileType, String fileId) fileRemoved,
    required TResult Function(FileType fileType) errorCleared,
  }) {
    return dragLeft(fileType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAllSections,
    TResult? Function(FileType fileType)? loadSection,
    TResult? Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult? Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult? Function(FileType fileType)? dragEntered,
    TResult? Function(FileType fileType)? dragLeft,
    TResult? Function(FileType fileType, String fileId)? fileRemoved,
    TResult? Function(FileType fileType)? errorCleared,
  }) {
    return dragLeft?.call(fileType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAllSections,
    TResult Function(FileType fileType)? loadSection,
    TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult Function(FileType fileType)? dragEntered,
    TResult Function(FileType fileType)? dragLeft,
    TResult Function(FileType fileType, String fileId)? fileRemoved,
    TResult Function(FileType fileType)? errorCleared,
    required TResult orElse(),
  }) {
    if (dragLeft != null) {
      return dragLeft(fileType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllSections value) loadAllSections,
    required TResult Function(_LoadSection value) loadSection,
    required TResult Function(_FileDropped value) fileDropped,
    required TResult Function(_FilePicked value) filePicked,
    required TResult Function(_DragEntered value) dragEntered,
    required TResult Function(_DragLeft value) dragLeft,
    required TResult Function(_FileRemoved value) fileRemoved,
    required TResult Function(_ErrorCleared value) errorCleared,
  }) {
    return dragLeft(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllSections value)? loadAllSections,
    TResult? Function(_LoadSection value)? loadSection,
    TResult? Function(_FileDropped value)? fileDropped,
    TResult? Function(_FilePicked value)? filePicked,
    TResult? Function(_DragEntered value)? dragEntered,
    TResult? Function(_DragLeft value)? dragLeft,
    TResult? Function(_FileRemoved value)? fileRemoved,
    TResult? Function(_ErrorCleared value)? errorCleared,
  }) {
    return dragLeft?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllSections value)? loadAllSections,
    TResult Function(_LoadSection value)? loadSection,
    TResult Function(_FileDropped value)? fileDropped,
    TResult Function(_FilePicked value)? filePicked,
    TResult Function(_DragEntered value)? dragEntered,
    TResult Function(_DragLeft value)? dragLeft,
    TResult Function(_FileRemoved value)? fileRemoved,
    TResult Function(_ErrorCleared value)? errorCleared,
    required TResult orElse(),
  }) {
    if (dragLeft != null) {
      return dragLeft(this);
    }
    return orElse();
  }
}

abstract class _DragLeft implements FileUploadEvent {
  const factory _DragLeft(final FileType fileType) = _$DragLeftImpl;

  FileType get fileType;

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DragLeftImplCopyWith<_$DragLeftImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FileRemovedImplCopyWith<$Res> {
  factory _$$FileRemovedImplCopyWith(
          _$FileRemovedImpl value, $Res Function(_$FileRemovedImpl) then) =
      __$$FileRemovedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({FileType fileType, String fileId});
}

/// @nodoc
class __$$FileRemovedImplCopyWithImpl<$Res>
    extends _$FileUploadEventCopyWithImpl<$Res, _$FileRemovedImpl>
    implements _$$FileRemovedImplCopyWith<$Res> {
  __$$FileRemovedImplCopyWithImpl(
      _$FileRemovedImpl _value, $Res Function(_$FileRemovedImpl) _then)
      : super(_value, _then);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileType = null,
    Object? fileId = null,
  }) {
    return _then(_$FileRemovedImpl(
      fileType: null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as FileType,
      fileId: null == fileId
          ? _value.fileId
          : fileId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FileRemovedImpl implements _FileRemoved {
  const _$FileRemovedImpl({required this.fileType, required this.fileId});

  @override
  final FileType fileType;
  @override
  final String fileId;

  @override
  String toString() {
    return 'FileUploadEvent.fileRemoved(fileType: $fileType, fileId: $fileId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FileRemovedImpl &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType) &&
            (identical(other.fileId, fileId) || other.fileId == fileId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fileType, fileId);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FileRemovedImplCopyWith<_$FileRemovedImpl> get copyWith =>
      __$$FileRemovedImplCopyWithImpl<_$FileRemovedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAllSections,
    required TResult Function(FileType fileType) loadSection,
    required TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)
        fileDropped,
    required TResult Function(FileType fileType, picker.PlatformFile file)
        filePicked,
    required TResult Function(FileType fileType) dragEntered,
    required TResult Function(FileType fileType) dragLeft,
    required TResult Function(FileType fileType, String fileId) fileRemoved,
    required TResult Function(FileType fileType) errorCleared,
  }) {
    return fileRemoved(fileType, fileId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAllSections,
    TResult? Function(FileType fileType)? loadSection,
    TResult? Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult? Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult? Function(FileType fileType)? dragEntered,
    TResult? Function(FileType fileType)? dragLeft,
    TResult? Function(FileType fileType, String fileId)? fileRemoved,
    TResult? Function(FileType fileType)? errorCleared,
  }) {
    return fileRemoved?.call(fileType, fileId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAllSections,
    TResult Function(FileType fileType)? loadSection,
    TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult Function(FileType fileType)? dragEntered,
    TResult Function(FileType fileType)? dragLeft,
    TResult Function(FileType fileType, String fileId)? fileRemoved,
    TResult Function(FileType fileType)? errorCleared,
    required TResult orElse(),
  }) {
    if (fileRemoved != null) {
      return fileRemoved(fileType, fileId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllSections value) loadAllSections,
    required TResult Function(_LoadSection value) loadSection,
    required TResult Function(_FileDropped value) fileDropped,
    required TResult Function(_FilePicked value) filePicked,
    required TResult Function(_DragEntered value) dragEntered,
    required TResult Function(_DragLeft value) dragLeft,
    required TResult Function(_FileRemoved value) fileRemoved,
    required TResult Function(_ErrorCleared value) errorCleared,
  }) {
    return fileRemoved(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllSections value)? loadAllSections,
    TResult? Function(_LoadSection value)? loadSection,
    TResult? Function(_FileDropped value)? fileDropped,
    TResult? Function(_FilePicked value)? filePicked,
    TResult? Function(_DragEntered value)? dragEntered,
    TResult? Function(_DragLeft value)? dragLeft,
    TResult? Function(_FileRemoved value)? fileRemoved,
    TResult? Function(_ErrorCleared value)? errorCleared,
  }) {
    return fileRemoved?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllSections value)? loadAllSections,
    TResult Function(_LoadSection value)? loadSection,
    TResult Function(_FileDropped value)? fileDropped,
    TResult Function(_FilePicked value)? filePicked,
    TResult Function(_DragEntered value)? dragEntered,
    TResult Function(_DragLeft value)? dragLeft,
    TResult Function(_FileRemoved value)? fileRemoved,
    TResult Function(_ErrorCleared value)? errorCleared,
    required TResult orElse(),
  }) {
    if (fileRemoved != null) {
      return fileRemoved(this);
    }
    return orElse();
  }
}

abstract class _FileRemoved implements FileUploadEvent {
  const factory _FileRemoved(
      {required final FileType fileType,
      required final String fileId}) = _$FileRemovedImpl;

  FileType get fileType;
  String get fileId;

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FileRemovedImplCopyWith<_$FileRemovedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorClearedImplCopyWith<$Res> {
  factory _$$ErrorClearedImplCopyWith(
          _$ErrorClearedImpl value, $Res Function(_$ErrorClearedImpl) then) =
      __$$ErrorClearedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({FileType fileType});
}

/// @nodoc
class __$$ErrorClearedImplCopyWithImpl<$Res>
    extends _$FileUploadEventCopyWithImpl<$Res, _$ErrorClearedImpl>
    implements _$$ErrorClearedImplCopyWith<$Res> {
  __$$ErrorClearedImplCopyWithImpl(
      _$ErrorClearedImpl _value, $Res Function(_$ErrorClearedImpl) _then)
      : super(_value, _then);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileType = null,
  }) {
    return _then(_$ErrorClearedImpl(
      null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as FileType,
    ));
  }
}

/// @nodoc

class _$ErrorClearedImpl implements _ErrorCleared {
  const _$ErrorClearedImpl(this.fileType);

  @override
  final FileType fileType;

  @override
  String toString() {
    return 'FileUploadEvent.errorCleared(fileType: $fileType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorClearedImpl &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fileType);

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorClearedImplCopyWith<_$ErrorClearedImpl> get copyWith =>
      __$$ErrorClearedImplCopyWithImpl<_$ErrorClearedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAllSections,
    required TResult Function(FileType fileType) loadSection,
    required TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)
        fileDropped,
    required TResult Function(FileType fileType, picker.PlatformFile file)
        filePicked,
    required TResult Function(FileType fileType) dragEntered,
    required TResult Function(FileType fileType) dragLeft,
    required TResult Function(FileType fileType, String fileId) fileRemoved,
    required TResult Function(FileType fileType) errorCleared,
  }) {
    return errorCleared(fileType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAllSections,
    TResult? Function(FileType fileType)? loadSection,
    TResult? Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult? Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult? Function(FileType fileType)? dragEntered,
    TResult? Function(FileType fileType)? dragLeft,
    TResult? Function(FileType fileType, String fileId)? fileRemoved,
    TResult? Function(FileType fileType)? errorCleared,
  }) {
    return errorCleared?.call(fileType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAllSections,
    TResult Function(FileType fileType)? loadSection,
    TResult Function(FileType fileType, DropzoneFileInterface file,
            DropzoneViewController controller)?
        fileDropped,
    TResult Function(FileType fileType, picker.PlatformFile file)? filePicked,
    TResult Function(FileType fileType)? dragEntered,
    TResult Function(FileType fileType)? dragLeft,
    TResult Function(FileType fileType, String fileId)? fileRemoved,
    TResult Function(FileType fileType)? errorCleared,
    required TResult orElse(),
  }) {
    if (errorCleared != null) {
      return errorCleared(fileType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllSections value) loadAllSections,
    required TResult Function(_LoadSection value) loadSection,
    required TResult Function(_FileDropped value) fileDropped,
    required TResult Function(_FilePicked value) filePicked,
    required TResult Function(_DragEntered value) dragEntered,
    required TResult Function(_DragLeft value) dragLeft,
    required TResult Function(_FileRemoved value) fileRemoved,
    required TResult Function(_ErrorCleared value) errorCleared,
  }) {
    return errorCleared(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllSections value)? loadAllSections,
    TResult? Function(_LoadSection value)? loadSection,
    TResult? Function(_FileDropped value)? fileDropped,
    TResult? Function(_FilePicked value)? filePicked,
    TResult? Function(_DragEntered value)? dragEntered,
    TResult? Function(_DragLeft value)? dragLeft,
    TResult? Function(_FileRemoved value)? fileRemoved,
    TResult? Function(_ErrorCleared value)? errorCleared,
  }) {
    return errorCleared?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllSections value)? loadAllSections,
    TResult Function(_LoadSection value)? loadSection,
    TResult Function(_FileDropped value)? fileDropped,
    TResult Function(_FilePicked value)? filePicked,
    TResult Function(_DragEntered value)? dragEntered,
    TResult Function(_DragLeft value)? dragLeft,
    TResult Function(_FileRemoved value)? fileRemoved,
    TResult Function(_ErrorCleared value)? errorCleared,
    required TResult orElse(),
  }) {
    if (errorCleared != null) {
      return errorCleared(this);
    }
    return orElse();
  }
}

abstract class _ErrorCleared implements FileUploadEvent {
  const factory _ErrorCleared(final FileType fileType) = _$ErrorClearedImpl;

  FileType get fileType;

  /// Create a copy of FileUploadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorClearedImplCopyWith<_$ErrorClearedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
