name: pr_danger_checks

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types: [opened, edited, synchronize, reopened, labeled, unlabeled]
    branches:
      - master

permissions:
  contents: read
  pull-requests: write
  issues: write
  statuses: write
  checks: write

jobs:
  danger-checks:
    runs-on: ubuntu-latest
    steps:
      - name: 📚 Git Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: 📦 Install Danger
        run: npm install danger

      - name: 🧪 Run Danger
        run: npx danger ci --dangerfile scripts/danger/Dangerfile.js
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
