name: Semgrep

on:
  pull_request:
    branches: ["master"]
  push:
    branches: ["master"]

jobs:
  semgrep:
    name: Semgrep-Scan
    runs-on: ubuntu-latest
    container:
      image: returntocorp/semgrep:latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Semgrep Dependency Scan
        run: semgrep ci --supply-chain
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
        shell: bash
      - name: Run Code Scan on push
        if: github.event_name == 'push'
        run: semgrep ci --code || true
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
        shell: bash
      - name: Run Code scan on pull
        if: github.event_name == 'pull_request'
        run: semgrep ci --code
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
        shell: bash
