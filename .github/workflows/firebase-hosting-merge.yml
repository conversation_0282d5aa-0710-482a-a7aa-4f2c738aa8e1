# Deploy to WIP environment when merging to main
name: Deploy to WIP Environment

on:
  push:
    branches:
      - master

jobs:
  deploy-to-wip:
    runs-on: ubuntu-latest
    steps:
      - name: 📚 Git Checkout
        uses: actions/checkout@v4

      - name: 🐣 Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          cache: true

      - name: 📦 Install Dependencies
        run: flutter pub get

      - name: 🔍 Pre-deployment Analysis
        run: flutter analyze --fatal-infos

      - name: 🏗️ Build Flutter Web
        run: flutter build web --release --dart-define=environment=wip

      - name: 🚀 Deploy to WIP Environment
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_MARKETINIYA_WIP }}
          channelId: live
          projectId: marketiniya-wip
          target: wip

      - name: ✅ Deployment Success
        run: |
          echo "🚀 Successfully deployed to WIP environment!"
          echo "🌐 WIP URL: https://marketiniya-wip.web.app"
