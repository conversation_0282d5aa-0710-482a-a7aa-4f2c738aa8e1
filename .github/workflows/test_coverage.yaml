name: test_coverage

on:
  pull_request:
    branches: [master]
  push:
    branches: [master]

jobs:
  test-coverage:
    runs-on: ubuntu-latest
    steps:
      - name: 📚 Git Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🐣 Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          cache: true

      - name: 📦 Install Dependencies
        run: flutter pub get

      # - name: 🧪 Run All Tests with Coverage
      #   run: |
      #     flutter test --coverage --test-randomize-ordering-seed random
      #     sudo apt-get update
      #     sudo apt-get install -y lcov
      #     lcov --remove coverage/lcov.info \
      #       '*/generated/*' \
      #       '*/l10n/*' \
      #       '*/test/*' \
      #       '*/tests/*' \
      #       '*/.dart_tool/*' \
      #       '*/build/*' \
      #       '*/*.g.dart' \
      #       '*/*.freezed.dart' \
      #       '*/*.mocks.dart' \
      #       -o coverage/lcov_cleaned.info
      #     genhtml coverage/lcov_cleaned.info -o coverage/html
      #     lcov --summary coverage/lcov_cleaned.info

      # - name: 📊 Coverage Summary
      #   run: |
      #     COVERAGE=$(lcov --summary coverage/lcov_cleaned.info 2>&1 | grep "lines" | grep -o '[0-9.]*%' | head -1)
      #     echo "Coverage: $COVERAGE"
      #     echo "COVERAGE_PERCENTAGE=$COVERAGE" >> $GITHUB_ENV
      #     echo "Coverage: $COVERAGE" > coverage/coverage.txt

      # - name: 📈 Upload Coverage to Codecov
      #   uses: codecov/codecov-action@v3
      #   with:
      #     file: coverage/lcov_cleaned.info
      #     flags: unittests
      #     name: codecov-umbrella
      #     fail_ci_if_error: false
      #     verbose: true

      # - name: 📋 Coverage Comment on PR
      #   if: github.event_name == 'pull_request'
      #   uses: actions/github-script@v7
      #   with:
      #     script: |
      #       const coverage = process.env.COVERAGE_PERCENTAGE;
      #       const comment = `## 📊 Test Coverage Report
      #
      #       **Coverage: ${coverage}**
      #
      #       - ✅ All tests passed
      #       - 📈 Coverage report generated
      #       - 🔗 [View detailed coverage report](https://codecov.io/gh/${{ github.repository }})
      #
      #       *Coverage calculated from: \`flutter test --coverage\`*`;
      #
      #       github.rest.issues.createComment({
      #         issue_number: context.issue.number,
      #         owner: context.repo.owner,
      #         repo: context.repo.repo,
      #         body: comment
      #       });

      # - name: 📁 Upload Coverage Artifacts
      #   uses: actions/upload-artifact@v3
      #   with:
      #     name: coverage-report
      #     path: |
      #       coverage/html/
      #       coverage/lcov_cleaned.info
      #       coverage/coverage.txt

      - name: ✅ Workflow Complete (Tests Disabled)
        run: echo "Test coverage workflow completed - tests are currently disabled"
